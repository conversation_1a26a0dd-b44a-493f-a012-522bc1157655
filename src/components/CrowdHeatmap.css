.crowd-heatmap-container {
  position: relative;
  background: #1a1a1a;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid #333;
}

.crowd-heatmap-error {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 500px;
  background: #1a1a1a;
  border-radius: 12px;
  border: 1px solid #333;
}

.error-content {
  text-align: center;
  color: #fff;
  padding: 40px;
}

.error-content h3 {
  color: #dc3545;
  margin: 0 0 16px 0;
  font-size: 1.5rem;
}

.error-content p {
  color: #bbb;
  margin: 0 0 24px 0;
  font-size: 1rem;
}

.retry-btn {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.map-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #2a2a2a;
  border-bottom: 1px solid #333;
  flex-wrap: wrap;
  gap: 16px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-group label {
  color: #bbb;
  font-size: 0.9rem;
  font-weight: 500;
}

.map-type-select {
  background: #1a1a1a;
  color: #fff;
  border: 1px solid #444;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 0.9rem;
  outline: none;
  cursor: pointer;
}

.map-type-select:focus {
  border-color: #007bff;
}

.heatmap-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-label {
  color: #bbb;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 4px;
}

.stat-value {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
}

.stat-value.critical {
  color: #dc3545;
}

.map-wrapper {
  position: relative;
  height: 500px;
  overflow: hidden;
}

.map-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  color: #fff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.map-element {
  transition: opacity 0.3s ease;
}

.heatmap-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  min-width: 180px;
  backdrop-filter: blur(10px);
}

.heatmap-legend h4 {
  color: #fff;
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.legend-gradient {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.legend-label {
  color: #bbb;
  font-size: 0.8rem;
  font-weight: 500;
}

.gradient-bar {
  flex: 1;
  height: 8px;
  border-radius: 4px;
  background: linear-gradient(
    to right,
    rgba(0, 255, 255, 0.8),
    rgba(0, 191, 255, 0.8),
    rgba(0, 127, 255, 0.8),
    rgba(0, 0, 255, 0.8),
    rgba(127, 0, 127, 0.8),
    rgba(255, 0, 0, 0.8)
  );
}

.legend-scale {
  display: flex;
  justify-content: space-between;
  color: #bbb;
  font-size: 0.7rem;
  margin-top: 4px;
}

.zone-legend {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(26, 26, 26, 0.9);
  border: 1px solid #333;
  border-radius: 8px;
  padding: 16px;
  backdrop-filter: blur(10px);
}

.zone-legend h4 {
  color: #fff;
  margin: 0 0 12px 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  opacity: 0.8;
}

.legend-color.normal {
  background: #44AA44;
}

.legend-color.warning {
  background: #FFAA44;
}

.legend-color.critical {
  background: #FF4444;
}

.legend-item span {
  color: #bbb;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Info Window Styles (injected globally) */
.density-info,
.zone-info {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  min-width: 250px;
  max-width: 300px;
}

.density-info h4,
.zone-info h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.density-info p,
.zone-info p {
  margin: 4px 0;
  color: #666;
  font-size: 0.9rem;
}

.status-critical {
  color: #dc3545;
  font-weight: 600;
}

.status-warning {
  color: #ffc107;
  font-weight: 600;
}

.status-normal {
  color: #28a745;
  font-weight: 600;
}

.zone-stats {
  margin-bottom: 12px;
}

.occupancy-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
}

.occupancy-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* Animations */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .map-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .heatmap-stats {
    justify-content: center;
    gap: 16px;
  }

  .map-wrapper {
    height: 400px;
  }

  .heatmap-legend,
  .zone-legend {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    margin: 16px;
    width: calc(100% - 32px);
  }

  .heatmap-legend {
    order: 1;
  }

  .zone-legend {
    order: 2;
  }

  .legend-items {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .crowd-heatmap-container {
    border-radius: 8px;
  }

  .map-controls {
    padding: 12px 16px;
  }

  .heatmap-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .map-wrapper {
    height: 300px;
  }

  .stat {
    font-size: 0.9rem;
  }

  .heatmap-legend,
  .zone-legend {
    margin: 12px;
    width: calc(100% - 24px);
    padding: 12px;
  }

  .density-info,
  .zone-info {
    min-width: 200px;
    max-width: 250px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .crowd-heatmap-container {
    border: 2px solid #fff;
  }

  .map-controls {
    background: #000;
    border-bottom: 2px solid #fff;
  }

  .heatmap-legend,
  .zone-legend {
    background: rgba(0, 0, 0, 0.95);
    border: 2px solid #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }

  .occupancy-fill {
    transition: none;
  }

  .map-element {
    transition: none;
  }
}
