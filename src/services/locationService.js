/**
 * Location Service for Drishti Dashboard
 * Handles geolocation functionality including getting current location,
 * managing permissions, and providing fallback options
 */

class LocationService {
  constructor() {
    this.currentLocation = null;
    this.isLocationSupported = 'geolocation' in navigator;
    this.watchId = null;
    this.locationCallbacks = new Set();
    this.errorCallbacks = new Set();
    
    // Default fallback location (San Francisco)
    this.fallbackLocation = {
      lat: 37.7749,
      lng: -122.4194,
      name: "Default Location",
      address: "San Francisco, CA",
      accuracy: null,
      timestamp: null,
      source: 'fallback'
    };
  }

  /**
   * Check if geolocation is supported by the browser
   */
  isSupported() {
    return this.isLocationSupported;
  }

  /**
   * Get current location with options
   * @param {Object} options - Geolocation options
   * @returns {Promise<Object>} Location object with lat, lng, accuracy, etc.
   */
  async getCurrentLocation(options = {}) {
    if (!this.isLocationSupported) {
      throw new Error('Geolocation is not supported by this browser');
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 300000 // 5 minutes
    };

    const geoOptions = { ...defaultOptions, ...options };

    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = this.formatLocationData(position);
          this.currentLocation = location;
          this.notifyLocationCallbacks(location);
          resolve(location);
        },
        (error) => {
          const errorInfo = this.handleGeolocationError(error);
          this.notifyErrorCallbacks(errorInfo);
          reject(errorInfo);
        },
        geoOptions
      );
    });
  }

  /**
   * Start watching location changes
   * @param {Object} options - Geolocation options
   * @returns {number} Watch ID
   */
  startWatching(options = {}) {
    if (!this.isLocationSupported) {
      throw new Error('Geolocation is not supported by this browser');
    }

    if (this.watchId !== null) {
      this.stopWatching();
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 60000 // 1 minute
    };

    const geoOptions = { ...defaultOptions, ...options };

    this.watchId = navigator.geolocation.watchPosition(
      (position) => {
        const location = this.formatLocationData(position);
        this.currentLocation = location;
        this.notifyLocationCallbacks(location);
      },
      (error) => {
        const errorInfo = this.handleGeolocationError(error);
        this.notifyErrorCallbacks(errorInfo);
      },
      geoOptions
    );

    return this.watchId;
  }

  /**
   * Stop watching location changes
   */
  stopWatching() {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId);
      this.watchId = null;
    }
  }

  /**
   * Format position data into standardized location object
   * @param {GeolocationPosition} position 
   * @returns {Object} Formatted location data
   */
  formatLocationData(position) {
    return {
      lat: position.coords.latitude,
      lng: position.coords.longitude,
      accuracy: position.coords.accuracy,
      altitude: position.coords.altitude,
      altitudeAccuracy: position.coords.altitudeAccuracy,
      heading: position.coords.heading,
      speed: position.coords.speed,
      timestamp: position.timestamp,
      source: 'geolocation',
      name: "Current Location",
      address: "Current Location"
    };
  }

  /**
   * Handle geolocation errors and provide user-friendly messages
   * @param {GeolocationPositionError} error 
   * @returns {Object} Error information
   */
  handleGeolocationError(error) {
    let message = '';
    let code = error.code;
    let suggestion = '';

    switch (error.code) {
      case error.PERMISSION_DENIED:
        message = 'Location access denied by user';
        suggestion = 'Please enable location permissions in your browser settings and refresh the page';
        break;
      case error.POSITION_UNAVAILABLE:
        message = 'Location information is unavailable';
        suggestion = 'Please check your internet connection and GPS settings';
        break;
      case error.TIMEOUT:
        message = 'Location request timed out';
        suggestion = 'Please try again or check your GPS signal';
        break;
      default:
        message = 'An unknown error occurred while retrieving location';
        suggestion = 'Please try again later';
        break;
    }

    return {
      code,
      message,
      suggestion,
      originalError: error,
      timestamp: Date.now()
    };
  }

  /**
   * Get fallback location when geolocation fails
   * @returns {Object} Fallback location data
   */
  getFallbackLocation() {
    return { ...this.fallbackLocation };
  }

  /**
   * Get current location or fallback
   * @returns {Object} Current or fallback location
   */
  getCurrentOrFallback() {
    return this.currentLocation || this.getFallbackLocation();
  }

  /**
   * Subscribe to location updates
   * @param {Function} callback - Function to call when location updates
   */
  onLocationUpdate(callback) {
    this.locationCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.locationCallbacks.delete(callback);
    };
  }

  /**
   * Subscribe to location errors
   * @param {Function} callback - Function to call when location errors occur
   */
  onLocationError(callback) {
    this.errorCallbacks.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.errorCallbacks.delete(callback);
    };
  }

  /**
   * Notify all location callbacks
   * @param {Object} location - Location data
   */
  notifyLocationCallbacks(location) {
    this.locationCallbacks.forEach(callback => {
      try {
        callback(location);
      } catch (error) {
        console.error('Error in location callback:', error);
      }
    });
  }

  /**
   * Notify all error callbacks
   * @param {Object} error - Error information
   */
  notifyErrorCallbacks(error) {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        console.error('Error in error callback:', err);
      }
    });
  }

  /**
   * Calculate distance between two points in meters
   * @param {Object} point1 - {lat, lng}
   * @param {Object} point2 - {lat, lng}
   * @returns {number} Distance in meters
   */
  calculateDistance(point1, point2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = point1.lat * Math.PI / 180;
    const φ2 = point2.lat * Math.PI / 180;
    const Δφ = (point2.lat - point1.lat) * Math.PI / 180;
    const Δλ = (point2.lng - point1.lng) * Math.PI / 180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  /**
   * Check if location has changed significantly
   * @param {Object} newLocation - New location data
   * @param {number} threshold - Distance threshold in meters (default: 10m)
   * @returns {boolean} True if location changed significantly
   */
  hasLocationChanged(newLocation, threshold = 10) {
    if (!this.currentLocation) return true;
    
    const distance = this.calculateDistance(this.currentLocation, newLocation);
    return distance > threshold;
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.stopWatching();
    this.locationCallbacks.clear();
    this.errorCallbacks.clear();
    this.currentLocation = null;
  }
}

// Create singleton instance
const locationService = new LocationService();

export default locationService;
